#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建演示用的LSTM模型文件
"""

import numpy as np
import pickle
import os

# 在全局范围定义类以避免pickle问题
class MockScaler:
    def __init__(self):
        self.mean_ = np.random.random(13)
        self.scale_ = np.random.random(13) + 0.5

    def transform(self, X):
        return (X - self.mean_) / self.scale_

    def inverse_transform(self, X):
        return X * self.scale_ + self.mean_

def create_demo_model():
    """创建演示用的模型文件"""

    # 创建模型目录
    model_dir = "A股模型"
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    
    # 创建模拟的LSTM模型
    class MockLSTMModel:
        def __init__(self):
            self.weights = np.random.random((13, 1))
            self.bias = np.random.random(1)
        
        def predict(self, X, verbose=0):
            # 简单的线性预测作为演示
            if len(X.shape) == 3:
                # 取最后一个时间步的数据
                last_step = X[0, -1, :]
                prediction = np.dot(last_step, self.weights) + self.bias
                return prediction.reshape(1, 1)
            return np.array([[0.0]])
        
        def save(self, filepath):
            """保存模型"""
            model_data = {
                'weights': self.weights,
                'bias': self.bias
            }
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"✅ 模型已保存到: {filepath}")
        
        @classmethod
        def load(cls, filepath):
            """加载模型"""
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            model = cls()
            model.weights = model_data['weights']
            model.bias = model_data['bias']
            return model
    
    # 创建并保存模型
    model = MockLSTMModel()
    model.save(os.path.join(model_dir, "trained_model.pkl"))
    
    # 创建并保存标准化器
    scaler = MockScaler()
    with open(os.path.join(model_dir, "scaler.pkl"), 'wb') as f:
        pickle.dump(scaler, f)
    print(f"✅ 标准化器已保存到: {os.path.join(model_dir, 'scaler.pkl')}")
    
    print("🎉 演示模型创建完成！")
    print("注意：这是一个演示模型，仅用于测试系统功能")

if __name__ == "__main__":
    create_demo_model()
