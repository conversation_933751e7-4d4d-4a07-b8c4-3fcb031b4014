#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件 - 综合选股系统
"""

import os

# ==================== API配置 ====================
# Tushare配置
TUSHARE_TOKEN = 'c3ca216a9232afe1c5b5939d0705e0c2950ffa355449ed10fe5ff5f0'

# 阿里云通义千问配置
LLM_API_KEY = 'sk-17b7ed14b1bd4e80b311a5c696a7468c'
LLM_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
LLM_MODEL = 'qwen-plus'

# ==================== 模型路径配置 ====================
# 基础路径
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
MODEL_DIR = os.path.join(BASE_DIR, "A股模型")

# LSTM模型文件路径
LSTM_MODEL_PATHS = {
    'v2.4.5': os.path.join(MODEL_DIR, "股票预测模型-2.4.5-中国A股.h5"),
    'v1.3': os.path.join(MODEL_DIR, "股票预测模型-1.3-中国A股.h5"),
    'current': os.path.join(MODEL_DIR, "trained_model.pkl")  # 当前使用的演示模型
}

# 数据预处理器路径
SCALER_PATHS = {
    'v2.4.5': os.path.join(MODEL_DIR, "scaler_v2.4.5.pkl"),
    'v1.3': os.path.join(MODEL_DIR, "scaler_v1.3.pkl"),
    'current': os.path.join(MODEL_DIR, "scaler.pkl")
}

# ==================== 模型参数配置 ====================
# LSTM模型v2.4.5的13个特征
LSTM_FEATURES_V245 = [
    'close', 'open', 'high', 'low', 'vol',      # OHLCV基础数据
    'MA_10', 'MA_30',                           # 移动平均线
    'RSI_14',                                   # RSI指标
    'MACD', 'MACD_signal',                      # MACD指标
    'Bollinger_Upper', 'Bollinger_Middle', 'Bollinger_Lower'  # 布林带
]

# LSTM模型v1.3的特征（主要是价格和成交量）
LSTM_FEATURES_V13 = [
    'close', 'vol'  # 收盘价和成交量
]

# 当前使用的特征集
CURRENT_FEATURES = LSTM_FEATURES_V245

# 模型输入参数
LSTM_PARAMS = {
    'sequence_length': 60,      # 时间序列长度
    'prediction_days': 3,       # 预测天数
    'batch_size': 32,          # 批次大小
    'epochs': 100              # 训练轮数
}

# ==================== 选股参数配置 ====================
# 技术指标参数
TECHNICAL_PARAMS = {
    'MA_SHORT': 10,            # 短期均线
    'MA_LONG': 30,             # 长期均线
    'RSI_PERIOD': 14,          # RSI周期
    'MACD_FAST': 12,           # MACD快线
    'MACD_SLOW': 26,           # MACD慢线
    'MACD_SIGNAL': 9,          # MACD信号线
    'BOLLINGER_PERIOD': 20,    # 布林带周期
    'BOLLINGER_STD': 2,        # 布林带标准差
    'KDJ_K': 9,                # KDJ K值周期
    'KDJ_D': 3,                # KDJ D值周期
    'ATR_PERIOD': 14,          # ATR周期
    'VOLUME_MA': 5             # 成交量均线
}

# 评分权重配置
SCORING_WEIGHTS = {
    'RSI': 20,                 # RSI指标权重
    'MACD': 20,                # MACD指标权重
    'BOLLINGER': 15,           # 布林带权重
    'VOLUME': 15,              # 成交量权重
    'KDJ': 10,                 # KDJ指标权重
    'TREND': 10,               # 趋势权重
    'VOLATILITY': 10           # 波动率权重
}

# 筛选条件
FILTER_CONDITIONS = {
    'min_price': 3.0,          # 最低价格
    'max_price': 300.0,        # 最高价格
    'min_volume': 1000000,     # 最小成交量（手）
    'min_market_cap': 50,      # 最小市值（亿元）
    'max_pe': 100,             # 最大市盈率
    'min_tech_score': 60,      # 最低技术评分
    'target_return': 7.0,      # 目标收益率(%)
    'holding_days': 3          # 持有天数
}

# ==================== 数据源配置 ====================
# 股票池配置
STOCK_POOLS = {
    'demo': [  # 演示股票池
        ("000001.SZ", "平安银行"),
        ("000002.SZ", "万科A"),
        ("600519.SH", "贵州茅台"),
        ("000858.SZ", "五粮液"),
        ("300059.SZ", "东方财富"),
        ("300750.SZ", "宁德时代"),
        ("002415.SZ", "海康威视"),
        ("600036.SH", "招商银行"),
        ("002594.SZ", "比亚迪"),
        ("600276.SH", "恒瑞医药")
    ],
    'active': [],  # 活跃股票池（从Tushare动态获取）
    'custom': []   # 自定义股票池
}

# 数据获取参数
DATA_PARAMS = {
    'history_days': 90,        # 历史数据天数
    'update_interval': 300,    # 数据更新间隔（秒）
    'retry_times': 3,          # 重试次数
    'timeout': 30              # 超时时间（秒）
}

# ==================== 输出配置 ====================
# 结果输出
OUTPUT_CONFIG = {
    'max_results': 10,         # 最大输出结果数
    'save_results': True,      # 是否保存结果
    'result_format': 'json',   # 结果格式：json/csv/excel
    'output_dir': 'results',   # 输出目录
    'include_charts': False    # 是否包含图表
}

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',           # 日志级别
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': 'stock_selector.log',
    'max_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# ==================== 风险控制配置 ====================
RISK_CONFIG = {
    'max_position': 0.1,       # 单只股票最大仓位
    'stop_loss': -0.05,        # 止损比例
    'take_profit': 0.07,       # 止盈比例
    'max_drawdown': -0.15,     # 最大回撤
    'risk_free_rate': 0.03     # 无风险利率
}

# ==================== 回测配置 ====================
BACKTEST_CONFIG = {
    'initial_capital': 100000,  # 初始资金
    'commission': 0.0003,       # 手续费率
    'slippage': 0.001,         # 滑点
    'benchmark': '000300.SH',   # 基准指数（沪深300）
    'start_date': '2023-01-01', # 回测开始日期
    'end_date': '2024-12-31'    # 回测结束日期
}

# ==================== 实用函数 ====================
def get_model_path(version='current'):
    """获取模型路径"""
    return LSTM_MODEL_PATHS.get(version, LSTM_MODEL_PATHS['current'])

def get_scaler_path(version='current'):
    """获取标准化器路径"""
    return SCALER_PATHS.get(version, SCALER_PATHS['current'])

def get_features(version='current'):
    """获取特征列表"""
    if version == 'v1.3':
        return LSTM_FEATURES_V13
    else:
        return LSTM_FEATURES_V245

def validate_config():
    """验证配置"""
    errors = []
    
    # 检查API密钥
    if not TUSHARE_TOKEN:
        errors.append("Tushare Token未配置")
    
    if not LLM_API_KEY:
        errors.append("LLM API Key未配置")
    
    # 检查模型目录
    if not os.path.exists(MODEL_DIR):
        errors.append(f"模型目录不存在: {MODEL_DIR}")
    
    # 检查权重总和
    if sum(SCORING_WEIGHTS.values()) != 100:
        errors.append("评分权重总和不等于100")
    
    return errors

if __name__ == "__main__":
    # 配置验证
    errors = validate_config()
    if errors:
        print("❌ 配置错误:")
        for error in errors:
            print(f"   - {error}")
    else:
        print("✅ 配置验证通过")
        print(f"📁 模型目录: {MODEL_DIR}")
        print(f"🤖 当前模型: {get_model_path()}")
        print(f"📊 特征数量: {len(get_features())}")
