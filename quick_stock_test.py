#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
狼眼看股市 - 快速功能测试
测试股票分析功能和AI调用
"""

import pandas as pd
import numpy as np
import tushare as ts
import requests
import time
import json
from datetime import datetime, timedelta

# 配置信息
TUSHARE_TOKEN = 'c3ca216a9232afe1c5b5939d0705e0c2950ffa355449ed10fe5ff5f0'
LLM_API_KEY = 'sk-17b7ed14b1bd4e80b311a5c696a7468c'
LLM_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'

# 初始化Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def call_ai_agent(prompt, model="qwen-plus"):
    """调用通义千问AI智能体"""
    try:
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}]
        }
        
        response = requests.post(LLM_URL, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            return f"AI调用失败，状态码: {response.status_code}"
    except Exception as e:
        return f"AI调用异常: {str(e)}"

def test_tushare_connection():
    """测试Tushare连接"""
    print("🔗 测试Tushare连接...")
    try:
        # 获取少量股票基本信息
        stock_basic = pro.stock_basic(exchange='SSE', list_status='L')
        print(f"✅ Tushare连接成功，获取到 {len(stock_basic)} 只沪市股票")
        return stock_basic.head(10)  # 返回前10只股票用于测试
    except Exception as e:
        print(f"❌ Tushare连接失败: {e}")
        return pd.DataFrame()

def test_ai_analysis(stocks_df):
    """测试AI分析功能"""
    print("🧠 测试AI股票分析...")
    
    if stocks_df.empty:
        print("❌ 没有股票数据，跳过AI测试")
        return
    
    # 选择前3只股票进行AI分析
    test_stocks = stocks_df.head(3)
    
    for _, stock in test_stocks.iterrows():
        print(f"\n📊 分析股票: {stock['name']}({stock['ts_code']})")
        
        # 构建AI分析提示
        prompt = f"""
        请分析股票 {stock['name']}({stock['ts_code']})：
        - 所属行业: {stock['industry']}
        - 上市日期: {stock['list_date']}
        
        请从以下角度简要分析：
        1. 行业前景
        2. 投资建议（买入/持有/卖出）
        3. 风险提示
        
        请用简洁的语言回答，不超过200字。
        """
        
        ai_response = call_ai_agent(prompt)
        print(f"🤖 AI分析结果:")
        print(ai_response[:300] + "..." if len(ai_response) > 300 else ai_response)
        print("-" * 50)
        
        time.sleep(1)  # 避免API限制

def test_stock_data_retrieval():
    """测试股票数据获取"""
    print("📈 测试股票数据获取...")
    
    try:
        # 测试获取贵州茅台的数据
        test_code = '600519.SH'  # 贵州茅台
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        # 获取日线数据
        daily_data = pro.daily(ts_code=test_code, start_date=start_date, end_date=end_date)
        
        if not daily_data.empty:
            print(f"✅ 成功获取 {test_code} 最近30天数据，共 {len(daily_data)} 条记录")
            print(f"📊 最新价格: {daily_data.iloc[0]['close']:.2f}元")
            print(f"📊 最新涨跌幅: {daily_data.iloc[0]['pct_chg']:.2f}%")
            
            # 计算简单技术指标
            avg_volume = daily_data['vol'].mean()
            price_volatility = daily_data['pct_chg'].std()
            
            print(f"📊 平均成交量: {avg_volume:.0f}手")
            print(f"📊 价格波动率: {price_volatility:.2f}%")
            
            return True
        else:
            print("❌ 未获取到股票数据")
            return False
            
    except Exception as e:
        print(f"❌ 股票数据获取失败: {e}")
        return False

def test_fund_flow_data():
    """测试资金流向数据"""
    print("💰 测试资金流向数据...")
    
    try:
        # 测试获取资金流向数据
        test_code = '600519.SH'
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')
        
        # 尝试获取资金流向数据
        try:
            moneyflow = pro.moneyflow(ts_code=test_code, start_date=start_date, end_date=end_date)
            if not moneyflow.empty:
                print(f"✅ 成功获取资金流向数据，共 {len(moneyflow)} 条记录")
                latest = moneyflow.iloc[0]
                print(f"💰 最新主力净流入: {latest['net_mf_amount']:.2f}万元")
                return True
            else:
                print("⚠️ 资金流向数据为空（可能需要更高权限）")
                return False
        except Exception as e:
            print(f"⚠️ 资金流向数据获取失败: {e}")
            print("💡 提示: 资金流向数据可能需要Tushare Pro高级权限")
            return False
            
    except Exception as e:
        print(f"❌ 资金流向测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🐺" + "="*60 + "🐺")
    print("🐺        狼眼看股市 - 快速功能测试        🐺")
    print("🐺" + "="*60 + "🐺")
    print()
    
    # 测试结果统计
    test_results = {
        'tushare_connection': False,
        'ai_analysis': False,
        'stock_data': False,
        'fund_flow': False
    }
    
    # 1. 测试Tushare连接
    stocks_df = test_tushare_connection()
    test_results['tushare_connection'] = not stocks_df.empty
    print()
    
    # 2. 测试股票数据获取
    test_results['stock_data'] = test_stock_data_retrieval()
    print()
    
    # 3. 测试资金流向数据
    test_results['fund_flow'] = test_fund_flow_data()
    print()
    
    # 4. 测试AI分析
    if test_results['tushare_connection']:
        test_ai_analysis(stocks_df)
        test_results['ai_analysis'] = True
    print()
    
    # 输出测试结果
    print("🎯" + "="*60 + "🎯")
    print("🎯                测试结果汇总                🎯")
    print("🎯" + "="*60 + "🎯")
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_name_cn = {
            'tushare_connection': 'Tushare连接',
            'ai_analysis': 'AI分析功能',
            'stock_data': '股票数据获取',
            'fund_flow': '资金流向数据'
        }
        print(f"{test_name_cn[test_name]}: {status}")
    
    success_count = sum(test_results.values())
    total_count = len(test_results)
    
    print()
    print(f"🎉 测试完成: {success_count}/{total_count} 项功能正常")
    
    if success_count >= 3:
        print("✅ 核心功能正常，可以运行完整的股票分析系统！")
    else:
        print("⚠️ 部分功能异常，建议检查配置后再运行完整系统")

if __name__ == "__main__":
    main()
