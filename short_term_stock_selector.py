#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
狼眼看股市 - 短线选股系统
专门筛选3日内预计涨幅7%+的A股股票
"""

import pandas as pd
import numpy as np
import tushare as ts
import requests
import time
import json
from datetime import datetime, timedelta

# 配置信息
TUSHARE_TOKEN = 'c3ca216a9232afe1c5b5939d0705e0c2950ffa355449ed10fe5ff5f0'
LLM_API_KEY = 'sk-17b7ed14b1bd4e80b311a5c696a7468c'
LLM_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'

# 初始化Tushare
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

def call_ai_agent(prompt, model="qwen-plus"):
    """调用AI智能体进行股票分析"""
    try:
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a professional short-term stock trading expert with deep knowledge of A-share market patterns."},
                {"role": "user", "content": prompt}
            ]
        }
        
        response = requests.post(LLM_URL, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            return f"AI调用失败，状态码: {response.status_code}"
    except Exception as e:
        return f"AI调用异常: {str(e)}"

def get_active_stocks(limit=500):
    """获取活跃股票池"""
    try:
        print("📊 正在获取A股活跃股票池...")
        
        # 获取基本股票信息
        stock_basic = pro.stock_basic(exchange='', list_status='L')
        
        # 过滤掉ST股票和新股
        stock_basic = stock_basic[~stock_basic['name'].str.contains('ST|退')]
        
        # 获取最近交易数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=10)).strftime('%Y%m%d')
        
        active_stocks = []
        processed = 0
        
        for _, stock in stock_basic.head(limit).iterrows():
            try:
                # 获取最近10天数据
                daily_data = pro.daily(ts_code=stock['ts_code'], 
                                     start_date=start_date, 
                                     end_date=end_date)
                
                if not daily_data.empty and len(daily_data) >= 5:
                    # 计算短线指标
                    latest = daily_data.iloc[0]
                    avg_volume = daily_data['vol'].mean()
                    price_volatility = daily_data['pct_chg'].std()
                    recent_performance = daily_data['pct_chg'].head(3).mean()
                    
                    # 短线活跃度评分
                    score = 0
                    
                    # 成交量活跃 (权重30%)
                    if avg_volume > 50000:  # 5万手以上
                        score += 30
                    elif avg_volume > 20000:
                        score += 20
                    
                    # 价格波动适中 (权重25%)
                    if 2 < price_volatility < 6:  # 适度波动
                        score += 25
                    elif 1 < price_volatility < 8:
                        score += 15
                    
                    # 近期表现 (权重25%)
                    if recent_performance > 0:  # 近期上涨
                        score += 25
                    elif recent_performance > -2:  # 小幅下跌
                        score += 10
                    
                    # 价格位置 (权重20%)
                    if 5 < latest['close'] < 50:  # 价格适中
                        score += 20
                    elif 3 < latest['close'] < 100:
                        score += 10
                    
                    if score >= 60:  # 活跃度阈值
                        active_stocks.append({
                            'ts_code': stock['ts_code'],
                            'name': stock['name'],
                            'industry': stock['industry'],
                            'current_price': latest['close'],
                            'change_pct': latest['pct_chg'],
                            'volume': latest['vol'],
                            'turnover': latest['amount'],
                            'activity_score': score,
                            'volatility': price_volatility,
                            'recent_perf': recent_performance
                        })
                
                processed += 1
                if processed % 50 == 0:
                    print(f"   已处理 {processed}/{limit} 只股票，筛选出 {len(active_stocks)} 只活跃股")
                
                time.sleep(0.1)  # 避免API限制
                
            except Exception as e:
                continue
        
        active_df = pd.DataFrame(active_stocks)
        if not active_df.empty:
            active_df = active_df.sort_values('activity_score', ascending=False)
            print(f"✅ 筛选出 {len(active_df)} 只活跃股票")
            return active_df
        else:
            print("❌ 未找到符合条件的活跃股票")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"❌ 获取活跃股票失败: {e}")
        return pd.DataFrame()

def calculate_technical_indicators(stock_code, days=20):
    """计算技术指标"""
    try:
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days+10)).strftime('%Y%m%d')
        
        # 获取历史数据
        df = pro.daily(ts_code=stock_code, start_date=start_date, end_date=end_date)
        
        if df.empty or len(df) < 10:
            return None
        
        df = df.sort_values('trade_date').reset_index(drop=True)
        
        # 计算技术指标
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = df['close'].ewm(span=12).mean()
        ema26 = df['close'].ewm(span=26).mean()
        macd = ema12 - ema26
        signal = macd.ewm(span=9).mean()
        
        # 布林带
        ma20 = df['close'].rolling(window=20).mean()
        std20 = df['close'].rolling(window=20).std()
        upper_band = ma20 + (std20 * 2)
        lower_band = ma20 - (std20 * 2)
        
        # 最新值
        latest = df.iloc[-1]
        indicators = {
            'rsi': rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else 50,
            'macd': macd.iloc[-1] if not pd.isna(macd.iloc[-1]) else 0,
            'signal': signal.iloc[-1] if not pd.isna(signal.iloc[-1]) else 0,
            'bb_position': (latest['close'] - lower_band.iloc[-1]) / (upper_band.iloc[-1] - lower_band.iloc[-1]) if not pd.isna(upper_band.iloc[-1]) else 0.5,
            'volume_ratio': latest['vol'] / df['vol'].rolling(window=5).mean().iloc[-1] if df['vol'].rolling(window=5).mean().iloc[-1] > 0 else 1,
            'price_position': (latest['close'] - df['close'].rolling(window=20).min().iloc[-1]) / (df['close'].rolling(window=20).max().iloc[-1] - df['close'].rolling(window=20).min().iloc[-1]) if (df['close'].rolling(window=20).max().iloc[-1] - df['close'].rolling(window=20).min().iloc[-1]) > 0 else 0.5
        }
        
        return indicators
        
    except Exception as e:
        print(f"计算技术指标失败 {stock_code}: {e}")
        return None

def score_short_term_potential(stock_data, indicators):
    """评估短线上涨潜力"""
    score = 0
    reasons = []
    
    if indicators is None:
        return 0, ["技术指标计算失败"]
    
    # RSI指标 (25分)
    rsi = indicators['rsi']
    if 30 < rsi < 70:  # RSI适中，有上涨空间
        score += 25
        reasons.append(f"RSI适中({rsi:.1f})")
    elif 20 < rsi < 30:  # 超卖反弹
        score += 20
        reasons.append(f"RSI超卖反弹({rsi:.1f})")
    
    # MACD指标 (20分)
    macd = indicators['macd']
    signal = indicators['signal']
    if macd > signal and macd > 0:  # 金叉且在零轴上方
        score += 20
        reasons.append("MACD金叉向上")
    elif macd > signal:  # 金叉
        score += 15
        reasons.append("MACD金叉")
    
    # 布林带位置 (20分)
    bb_pos = indicators['bb_position']
    if 0.2 < bb_pos < 0.8:  # 在布林带中轨附近
        score += 20
        reasons.append("布林带中轨支撑")
    elif bb_pos < 0.3:  # 接近下轨，反弹机会
        score += 15
        reasons.append("布林带下轨反弹")
    
    # 成交量 (15分)
    vol_ratio = indicators['volume_ratio']
    if vol_ratio > 1.5:  # 放量
        score += 15
        reasons.append(f"放量({vol_ratio:.1f}倍)")
    elif vol_ratio > 1.2:
        score += 10
        reasons.append("温和放量")
    
    # 价格位置 (10分)
    price_pos = indicators['price_position']
    if 0.3 < price_pos < 0.7:  # 价格在中位
        score += 10
        reasons.append("价格中位有空间")
    
    # 近期表现 (10分)
    if stock_data['recent_perf'] > 0:
        score += 10
        reasons.append("近期表现强势")
    elif stock_data['recent_perf'] > -1:
        score += 5
        reasons.append("近期表现平稳")
    
    return score, reasons

def ai_final_selection(candidates):
    """AI最终筛选"""
    print("🧠 AI智能体进行最终筛选...")
    
    final_selections = []
    
    for _, stock in candidates.iterrows():
        prompt = f"""
        请分析股票 {stock['name']}({stock['ts_code']})的短线机会：
        
        基本信息：
        - 当前价格: {stock['current_price']:.2f}元
        - 今日涨跌: {stock['change_pct']:.2f}%
        - 所属行业: {stock['industry']}
        - 成交量: {stock['volume']:.0f}手
        - 活跃度评分: {stock['activity_score']}/100
        
        请判断该股票在未来3个交易日内是否有7%以上涨幅潜力，并给出：
        1. 上涨概率(0-100%)
        2. 预期涨幅(%)
        3. 关键买入信号
        4. 风险提示
        
        请用简洁专业的语言回答，格式：
        概率：XX%
        涨幅：XX%
        信号：XXX
        风险：XXX
        """
        
        ai_response = call_ai_agent(prompt)
        
        # 简单解析AI回复中的概率
        probability = 0
        expected_return = 0
        
        try:
            lines = ai_response.split('\n')
            for line in lines:
                if '概率' in line or '概率' in line:
                    prob_str = ''.join(filter(str.isdigit, line))
                    if prob_str:
                        probability = int(prob_str)
                elif '涨幅' in line:
                    return_str = ''.join(filter(lambda x: x.isdigit() or x == '.', line))
                    if return_str:
                        expected_return = float(return_str)
        except:
            pass
        
        if probability >= 60 and expected_return >= 7:  # AI认为概率>=60%且涨幅>=7%
            final_selections.append({
                'stock_info': stock,
                'ai_probability': probability,
                'ai_expected_return': expected_return,
                'ai_analysis': ai_response
            })
        
        time.sleep(1)  # 避免API限制
    
    return final_selections

def main():
    """主函数"""
    print("🐺" + "="*60 + "🐺")
    print("🐺        狼眼看股市 - 短线选股系统        🐺")
    print("🐺        目标：3日内涨幅7%+的A股        🐺")
    print("🐺" + "="*60 + "🐺")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 第一步：获取活跃股票
    active_stocks = get_active_stocks(limit=300)
    if active_stocks.empty:
        print("❌ 未找到活跃股票，程序退出")
        return
    
    print(f"\n📊 第一轮筛选完成，获得 {len(active_stocks)} 只候选股票")
    
    # 第二步：技术分析筛选
    print("\n🔍 第二步：技术指标分析...")
    candidates = []
    
    for idx, (_, stock) in enumerate(active_stocks.head(50).iterrows()):  # 取前50只进行详细分析
        print(f"   分析 {idx+1}/50: {stock['name']}")
        
        indicators = calculate_technical_indicators(stock['ts_code'])
        if indicators:
            score, reasons = score_short_term_potential(stock, indicators)
            
            if score >= 70:  # 技术面评分>=70分
                stock_dict = stock.to_dict()
                stock_dict['tech_score'] = score
                stock_dict['tech_reasons'] = reasons
                stock_dict['indicators'] = indicators
                candidates.append(stock_dict)
        
        time.sleep(0.2)
    
    if not candidates:
        print("❌ 技术分析未找到符合条件的股票")
        return
    
    candidates_df = pd.DataFrame(candidates)
    candidates_df = candidates_df.sort_values('tech_score', ascending=False)
    
    print(f"✅ 技术分析筛选出 {len(candidates_df)} 只候选股票")
    
    # 第三步：AI最终筛选
    print(f"\n🧠 第三步：AI智能筛选前 {min(20, len(candidates_df))} 只股票...")
    final_candidates = ai_final_selection(candidates_df.head(20))
    
    # 按AI评分排序，选出前10只
    final_candidates.sort(key=lambda x: x['ai_probability'] * x['ai_expected_return'], reverse=True)
    top_10 = final_candidates[:10]
    
    # 输出结果
    print("\n🎯" + "="*60 + "🎯")
    print("🎯            短线选股结果 (TOP 10)            🎯")
    print("🎯" + "="*60 + "🎯")
    
    if not top_10:
        print("❌ 未找到符合条件的股票")
        print("💡 建议：降低筛选标准或等待更好的市场时机")
    else:
        for i, selection in enumerate(top_10, 1):
            stock = selection['stock_info']
            print(f"\n{i}. {stock['name']}({stock['ts_code']})")
            print(f"   💰 当前价格: {stock['current_price']:.2f}元")
            print(f"   📈 AI预期涨幅: {selection['ai_expected_return']:.1f}%")
            print(f"   🎯 成功概率: {selection['ai_probability']}%")
            print(f"   🏭 所属行业: {stock['industry']}")
            print(f"   📊 技术评分: {stock['tech_score']}/100")
            print(f"   🔍 技术信号: {', '.join(stock['tech_reasons'][:3])}")
            print("-" * 50)
    
    print(f"\n⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("⚠️  风险提示: 短线交易风险较高，请严格控制仓位，设置止损！")

if __name__ == "__main__":
    main()
