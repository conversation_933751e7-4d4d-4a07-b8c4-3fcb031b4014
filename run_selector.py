#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动脚本 - 综合短线选股系统
"""

import sys
import os
from datetime import datetime

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'pandas', 'numpy', 'requests',
        'tushare', 'sklearn'
    ]

    optional_packages = [
        'tensorflow', 'keras'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_required.append(package)
    
    for package in optional_packages:
        try:
            __import__(package)
        except ImportError:
            missing_optional.append(package)
    
    if missing_required:
        print("❌ 缺少必需的依赖包:")
        for pkg in missing_required:
            print(f"   - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_required)}")
        return False
    
    if missing_optional:
        print("⚠️ 缺少可选的依赖包:")
        for pkg in missing_optional:
            print(f"   - {pkg}")
        print("LSTM功能将不可用，但技术指标分析仍可正常工作")
    
    return True

def main():
    """主启动函数"""
    print("🚀 启动综合短线选股系统...")
    print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        return
    
    print("✅ 依赖检查通过")
    print()
    
    try:
        # 导入并运行主程序
        from integrated_stock_selector import main as run_selector
        run_selector()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
