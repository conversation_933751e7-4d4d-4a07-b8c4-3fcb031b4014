#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
狼眼看股市 - 短线选股演示系统
基于真实A股特征的模拟数据演示短线选股逻辑
"""

import pandas as pd
import numpy as np
import requests
import time
import json
from datetime import datetime, timedelta
import random

# AI配置
LLM_API_KEY = 'sk-17b7ed14b1bd4e80b311a5c696a7468c'
LLM_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'

def call_ai_agent(prompt, model="qwen-plus"):
    """调用AI智能体进行股票分析"""
    try:
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a professional short-term stock trading expert specializing in A-share market analysis."},
                {"role": "user", "content": prompt}
            ]
        }
        
        response = requests.post(LLM_URL, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            return f"AI调用失败，状态码: {response.status_code}"
    except Exception as e:
        return f"AI调用异常: {str(e)}"

def generate_realistic_stock_data():
    """生成基于真实A股特征的模拟股票数据"""
    
    # 真实的A股行业和代表性股票
    stock_pool = [
        # 科技股
        {"code": "000002", "name": "万科A", "industry": "房地产", "base_price": 8.5},
        {"code": "000858", "name": "五粮液", "industry": "白酒", "base_price": 120.0},
        {"code": "002415", "name": "海康威视", "industry": "安防", "base_price": 32.0},
        {"code": "300059", "name": "东方财富", "industry": "证券", "base_price": 15.8},
        {"code": "300750", "name": "宁德时代", "industry": "新能源", "base_price": 180.0},
        
        # 传统行业
        {"code": "600036", "name": "招商银行", "industry": "银行", "base_price": 35.0},
        {"code": "600519", "name": "贵州茅台", "industry": "白酒", "base_price": 1680.0},
        {"code": "600887", "name": "伊利股份", "industry": "食品", "base_price": 28.0},
        {"code": "000001", "name": "平安银行", "industry": "银行", "base_price": 12.5},
        {"code": "002304", "name": "洋河股份", "industry": "白酒", "base_price": 95.0},
        
        # 新兴行业
        {"code": "688981", "name": "中芯国际", "industry": "芯片", "base_price": 45.0},
        {"code": "300142", "name": "沃森生物", "industry": "生物医药", "base_price": 25.0},
        {"code": "002594", "name": "比亚迪", "industry": "新能源汽车", "base_price": 220.0},
        {"code": "300014", "name": "亿纬锂能", "industry": "锂电池", "base_price": 35.0},
        {"code": "600276", "name": "恒瑞医药", "industry": "医药", "base_price": 42.0},
        
        # 周期股
        {"code": "600028", "name": "中国石化", "industry": "石油", "base_price": 5.2},
        {"code": "000858", "name": "五粮液", "industry": "白酒", "base_price": 120.0},
        {"code": "002142", "name": "宁波银行", "industry": "银行", "base_price": 28.0},
        {"code": "300124", "name": "汇川技术", "industry": "工控", "base_price": 55.0},
        {"code": "002027", "name": "分众传媒", "industry": "广告", "base_price": 6.8},
    ]
    
    stocks = []
    for stock_info in stock_pool:
        # 模拟真实的市场数据
        current_price = stock_info["base_price"] * (1 + random.uniform(-0.05, 0.05))
        change_pct = random.uniform(-3.0, 4.0)
        volume = random.randint(10000, 200000)  # 成交量(手)
        
        # 模拟技术指标
        rsi = random.uniform(25, 75)
        macd = random.uniform(-0.5, 0.8)
        bb_position = random.uniform(0.1, 0.9)
        volume_ratio = random.uniform(0.8, 2.5)
        
        # 计算短线潜力评分
        score = 0
        reasons = []
        
        # RSI评分
        if 30 < rsi < 70:
            score += 25
            reasons.append(f"RSI适中({rsi:.1f})")
        elif 20 < rsi < 30:
            score += 20
            reasons.append(f"RSI超卖({rsi:.1f})")
        
        # MACD评分
        if macd > 0:
            score += 20
            reasons.append("MACD金叉")
        elif macd > -0.2:
            score += 10
            reasons.append("MACD转强")
        
        # 布林带评分
        if 0.2 < bb_position < 0.8:
            score += 20
            reasons.append("布林带中轨")
        elif bb_position < 0.3:
            score += 15
            reasons.append("布林带下轨反弹")
        
        # 成交量评分
        if volume_ratio > 1.5:
            score += 15
            reasons.append(f"放量({volume_ratio:.1f}倍)")
        elif volume_ratio > 1.2:
            score += 10
            reasons.append("温和放量")
        
        # 行业热度加分
        hot_industries = ["新能源", "芯片", "生物医药", "新能源汽车"]
        if stock_info["industry"] in hot_industries:
            score += 10
            reasons.append("热门行业")
        
        stocks.append({
            "code": stock_info["code"],
            "name": stock_info["name"],
            "industry": stock_info["industry"],
            "current_price": round(current_price, 2),
            "change_pct": round(change_pct, 2),
            "volume": volume,
            "rsi": round(rsi, 1),
            "macd": round(macd, 3),
            "bb_position": round(bb_position, 2),
            "volume_ratio": round(volume_ratio, 1),
            "tech_score": score,
            "tech_reasons": reasons
        })
    
    return pd.DataFrame(stocks)

def ai_analyze_stock(stock):
    """AI分析单只股票的短线潜力"""
    prompt = f"""
    请分析A股股票 {stock['name']}({stock['code']})的短线交易机会：
    
    基本信息：
    - 当前价格: {stock['current_price']}元
    - 今日涨跌: {stock['change_pct']}%
    - 所属行业: {stock['industry']}
    - 成交量比: {stock['volume_ratio']}倍
    
    技术指标：
    - RSI: {stock['rsi']}
    - MACD: {stock['macd']}
    - 布林带位置: {stock['bb_position']}
    - 技术评分: {stock['tech_score']}/100
    
    请判断该股票在未来3个交易日内达到7%以上涨幅的可能性，并提供：
    
    1. 上涨概率: XX% (0-100之间的数字)
    2. 预期涨幅: XX% (预计涨幅百分比)
    3. 买入信号: 具体的技术信号
    4. 风险提示: 主要风险点
    
    请严格按照以下格式回答：
    概率：XX%
    涨幅：XX%
    信号：XXX
    风险：XXX
    """
    
    return call_ai_agent(prompt)

def parse_ai_response(response):
    """解析AI回复，提取关键数据"""
    probability = 0
    expected_return = 0
    signal = ""
    risk = ""
    
    try:
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if '概率' in line or 'probability' in line.lower():
                # 提取数字
                import re
                numbers = re.findall(r'\d+', line)
                if numbers:
                    probability = int(numbers[0])
            elif '涨幅' in line or 'return' in line.lower():
                import re
                numbers = re.findall(r'\d+\.?\d*', line)
                if numbers:
                    expected_return = float(numbers[0])
            elif '信号' in line or 'signal' in line.lower():
                signal = line.split('：')[-1].split(':')[-1].strip()
            elif '风险' in line or 'risk' in line.lower():
                risk = line.split('：')[-1].split(':')[-1].strip()
    except Exception as e:
        print(f"解析AI回复失败: {e}")
    
    return probability, expected_return, signal, risk

def main():
    """主函数"""
    print("🐺" + "="*70 + "🐺")
    print("🐺          狼眼看股市 - 短线选股演示系统          🐺")
    print("🐺          目标：筛选3日内涨幅7%+的A股          🐺")
    print("🐺" + "="*70 + "🐺")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 生成模拟股票数据
    print("📊 正在生成基于真实A股特征的候选股票...")
    stocks_df = generate_realistic_stock_data()
    
    # 第一轮：技术面筛选
    print("🔍 第一轮：技术指标筛选...")
    tech_candidates = stocks_df[stocks_df['tech_score'] >= 60].copy()
    tech_candidates = tech_candidates.sort_values('tech_score', ascending=False)
    
    print(f"✅ 技术面筛选出 {len(tech_candidates)} 只候选股票")
    
    if len(tech_candidates) == 0:
        print("❌ 技术面筛选未找到符合条件的股票")
        return
    
    # 第二轮：AI智能分析
    print(f"\n🧠 第二轮：AI智能分析前 {min(15, len(tech_candidates))} 只股票...")
    
    final_candidates = []
    
    for idx, (_, stock) in enumerate(tech_candidates.head(15).iterrows()):
        print(f"   🤖 AI分析 {idx+1}/15: {stock['name']}")
        
        ai_response = ai_analyze_stock(stock)
        probability, expected_return, signal, risk = parse_ai_response(ai_response)
        
        # 收集所有AI分析结果，降低筛选标准以展示更多候选股票
        final_candidates.append({
            'stock': stock,
            'ai_probability': probability,
            'ai_expected_return': expected_return,
            'ai_signal': signal,
            'ai_risk': risk,
            'ai_response': ai_response,
            'composite_score': probability * expected_return / 100,  # 综合评分
            'meets_criteria': probability >= 50 and expected_return >= 5  # 调整标准：概率≥50% 且 预期涨幅≥5%
        })
        
        time.sleep(1)  # 避免API限制
    
    # 按综合评分排序
    final_candidates.sort(key=lambda x: x['composite_score'], reverse=True)

    # 筛选符合条件的股票
    qualified_stocks = [c for c in final_candidates if c['meets_criteria']]
    top_10 = qualified_stocks[:10] if qualified_stocks else final_candidates[:10]

    # 输出最终结果
    print("\n🎯" + "="*70 + "🎯")
    print("🎯              短线选股结果 (TOP 10)              🎯")
    print("🎯" + "="*70 + "🎯")

    if not qualified_stocks:
        print("⚠️ 严格标准下未找到符合条件的股票（概率≥50% 且 预期涨幅≥5%）")
        print("📊 以下是按综合评分排序的候选股票供参考：\n")
    else:
        print(f"✅ 成功筛选出 {len(qualified_stocks)} 只符合条件的短线股票")
        if len(qualified_stocks) < 10:
            print(f"📊 补充显示其他候选股票，共展示前10只：\n")
        
        for i, candidate in enumerate(top_10, 1):
            stock = candidate['stock']
            # 标记是否符合严格标准
            status = "✅" if candidate['meets_criteria'] else "⚠️"

            print(f"{status}【{i:2d}】{stock['name']}({stock['code']})")
            print(f"     💰 当前价格: {stock['current_price']:8.2f}元    📈 今日涨跌: {stock['change_pct']:+5.1f}%")
            print(f"     🎯 成功概率: {candidate['ai_probability']:8d}%      📊 预期涨幅: {candidate['ai_expected_return']:5.1f}%")
            print(f"     🏭 所属行业: {stock['industry']:10s}    📈 技术评分: {stock['tech_score']:3d}/100")
            print(f"     🔍 买入信号: {candidate['ai_signal']}")
            print(f"     ⚠️  风险提示: {candidate['ai_risk']}")
            print(f"     📊 技术特征: {', '.join(stock['tech_reasons'][:3])}")
            print(f"     🤖 AI详细分析:")
            # 显示AI分析的前200字符
            ai_summary = candidate['ai_response'][:200].replace('\n', ' ') + "..."
            print(f"        {ai_summary}")
            print("-" * 70)
    
    print(f"\n⏰ 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 操作建议:")
    print("   1. 🎯 严格按照买入信号进场，设置7%止盈目标")
    print("   2. ⚠️  设置3-5%止损，严格执行风险控制")
    print("   3. ⏰ 持股周期控制在1-3个交易日内")
    print("   4. 💰 单只股票仓位不超过总资金的10%")
    print("   5. 📊 密切关注成交量变化和技术形态")
    
    print("\n⚠️  重要风险提示:")
    print("   • 短线交易风险极高，可能面临较大亏损")
    print("   • 市场变化快速，需要实时监控和快速决策")
    print("   • 建议先用小资金验证策略有效性")
    print("   • 严格遵守止损纪律，避免情绪化交易")

if __name__ == "__main__":
    main()
