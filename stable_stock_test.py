#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
狼眼看股市 - 稳定版股票分析测试
基于修复后的API配置，测试核心功能
"""

import pandas as pd
import numpy as np
import requests
import time
import json
from datetime import datetime, timedelta

# 使用修复后的API配置
LLM_API_KEY = 'sk-17b7ed14b1bd4e80b311a5c696a7468c'
LLM_URL = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'

def call_ai_agent(prompt, model="qwen-plus"):
    """调用修复后的AI智能体"""
    try:
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a professional stock market analyst."},
                {"role": "user", "content": prompt}
            ]
        }
        
        response = requests.post(LLM_URL, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"]
        else:
            return f"AI调用失败，状态码: {response.status_code}"
    except Exception as e:
        return f"AI调用异常: {str(e)}"

def test_ai_stock_analysis():
    """测试AI股票分析功能"""
    print("🧠 测试AI股票分析功能...")
    
    # 模拟股票数据
    test_stocks = [
        {"code": "600519", "name": "贵州茅台", "industry": "白酒", "price": 1680.50, "change": 2.3},
        {"code": "000001", "name": "平安银行", "industry": "银行", "price": 12.45, "change": -1.2},
        {"code": "300059", "name": "东方财富", "industry": "证券", "price": 15.80, "change": 3.5}
    ]
    
    for stock in test_stocks:
        print(f"\n📊 分析股票: {stock['name']}({stock['code']})")
        
        prompt = f"""
        请分析股票 {stock['name']}({stock['code']})：
        - 当前价格: {stock['price']}元
        - 今日涨跌: {stock['change']}%
        - 所属行业: {stock['industry']}
        
        请从以下角度简要分析：
        1. 技术面分析
        2. 投资建议（买入/持有/卖出）
        3. 风险提示
        
        请用简洁专业的语言回答，不超过150字。
        """
        
        ai_response = call_ai_agent(prompt)
        print(f"🤖 AI分析:")
        print(ai_response[:300] + "..." if len(ai_response) > 300 else ai_response)
        print("-" * 60)
        
        time.sleep(2)  # 避免API限制

def test_market_sentiment_analysis():
    """测试市场情绪分析"""
    print("📈 测试市场情绪分析...")
    
    market_data = {
        "上证指数": {"value": 3200, "change": 1.2},
        "深证成指": {"value": 10500, "change": 0.8},
        "创业板指": {"value": 2100, "change": -0.5}
    }
    
    prompt = f"""
    当前A股市场情况：
    - 上证指数: {market_data['上证指数']['value']} 点 ({market_data['上证指数']['change']:+.1f}%)
    - 深证成指: {market_data['深证成指']['value']} 点 ({market_data['深证成指']['change']:+.1f}%)
    - 创业板指: {market_data['创业板指']['value']} 点 ({market_data['创业板指']['change']:+.1f}%)
    
    请分析：
    1. 当前市场整体情绪
    2. 短期走势预判
    3. 投资策略建议
    
    请用专业简洁的语言回答，不超过200字。
    """
    
    ai_response = call_ai_agent(prompt)
    print(f"🤖 市场分析:")
    print(ai_response)
    print("-" * 60)

def test_sector_analysis():
    """测试行业分析"""
    print("🏭 测试行业分析...")
    
    sectors = ["新能源汽车", "人工智能", "生物医药"]
    
    for sector in sectors:
        print(f"\n🔍 分析行业: {sector}")
        
        prompt = f"""
        请分析 {sector} 行业：
        
        1. 当前发展阶段
        2. 主要投资机会
        3. 风险因素
        4. 推荐关注的细分领域
        
        请用专业简洁的语言回答，不超过150字。
        """
        
        ai_response = call_ai_agent(prompt)
        print(f"🤖 行业分析:")
        print(ai_response[:250] + "..." if len(ai_response) > 250 else ai_response)
        print("-" * 40)
        
        time.sleep(1)

def test_risk_assessment():
    """测试风险评估功能"""
    print("⚠️ 测试风险评估功能...")
    
    portfolio = [
        {"stock": "贵州茅台", "weight": 30, "sector": "消费"},
        {"stock": "宁德时代", "weight": 25, "sector": "新能源"},
        {"stock": "腾讯控股", "weight": 20, "sector": "科技"},
        {"stock": "招商银行", "weight": 15, "sector": "金融"},
        {"stock": "比亚迪", "weight": 10, "sector": "汽车"}
    ]
    
    portfolio_str = "\n".join([f"- {item['stock']}: {item['weight']}% ({item['sector']})" 
                              for item in portfolio])
    
    prompt = f"""
    请评估以下投资组合的风险：
    
    {portfolio_str}
    
    请分析：
    1. 行业集中度风险
    2. 个股权重风险
    3. 市场风险敞口
    4. 优化建议
    
    请用专业语言回答，不超过200字。
    """
    
    ai_response = call_ai_agent(prompt)
    print(f"🤖 风险评估:")
    print(ai_response)
    print("-" * 60)

def main():
    """主测试函数"""
    print("🐺" + "="*60 + "🐺")
    print("🐺        狼眼看股市 - 稳定版功能测试        🐺")
    print("🐺" + "="*60 + "🐺")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = []
    
    try:
        # 1. 测试个股分析
        print("=" * 60)
        test_ai_stock_analysis()
        test_results.append("✅ 个股分析")
        print()
        
        # 2. 测试市场情绪分析
        print("=" * 60)
        test_market_sentiment_analysis()
        test_results.append("✅ 市场情绪分析")
        print()
        
        # 3. 测试行业分析
        print("=" * 60)
        test_sector_analysis()
        test_results.append("✅ 行业分析")
        print()
        
        # 4. 测试风险评估
        print("=" * 60)
        test_risk_assessment()
        test_results.append("✅ 风险评估")
        print()
        
    except Exception as e:
        test_results.append(f"❌ 测试异常: {e}")
    
    # 输出测试结果
    print("🎯" + "="*60 + "🎯")
    print("🎯                测试结果汇总                🎯")
    print("🎯" + "="*60 + "🎯")
    
    for result in test_results:
        print(result)
    
    success_count = len([r for r in test_results if r.startswith("✅")])
    total_count = len(test_results)
    
    print()
    print(f"🎉 测试完成: {success_count}/{total_count} 项功能正常")
    print(f"⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == total_count:
        print("✅ 所有功能正常，股票分析系统可以正常运行！")
        return True
    else:
        print("⚠️ 部分功能异常，建议检查后再使用")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
