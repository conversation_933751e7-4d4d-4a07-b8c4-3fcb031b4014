#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建简单的演示模型文件
"""

import numpy as np
import pickle
import os

def create_simple_model():
    """创建简单的演示模型"""
    
    # 创建模型目录
    model_dir = "A股模型"
    if not os.path.exists(model_dir):
        os.makedirs(model_dir)
    
    # 创建简单的模型数据
    model_data = {
        'weights': np.random.random((13, 1)) * 0.1,  # 13个特征的权重
        'bias': np.array([0.02]),  # 偏置，预期2%的收益
        'model_type': 'simple_linear',
        'features': [
            'close', 'open', 'high', 'low', 'vol',
            'MA_10', 'MA_30', 'RSI_14', 'MACD', 'MACD_signal',
            'Bollinger_Upper', 'Bollinger_Middle', 'Bollinger_Lower'
        ]
    }
    
    # 保存模型
    model_path = os.path.join(model_dir, "trained_model.pkl")
    with open(model_path, 'wb') as f:
        pickle.dump(model_data, f)
    print(f"✅ 简单模型已保存到: {model_path}")
    
    # 创建简单的标准化器数据
    scaler_data = {
        'mean_': np.random.random(13) * 50,  # 特征均值
        'scale_': np.random.random(13) * 10 + 1,  # 特征标准差
        'scaler_type': 'simple_minmax'
    }
    
    # 保存标准化器
    scaler_path = os.path.join(model_dir, "scaler.pkl")
    with open(scaler_path, 'wb') as f:
        pickle.dump(scaler_data, f)
    print(f"✅ 简单标准化器已保存到: {scaler_path}")
    
    print("🎉 简单演示模型创建完成！")
    print("注意：这是一个演示模型，仅用于测试系统功能")

if __name__ == "__main__":
    create_simple_model()
