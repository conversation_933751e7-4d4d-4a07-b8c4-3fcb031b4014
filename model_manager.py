#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型管理器 - 管理LSTM模型的加载、预测和评估
"""

import os
import pickle
import numpy as np
import pandas as pd
import warnings
from datetime import datetime
from config import *

warnings.filterwarnings('ignore')

# 尝试导入深度学习框架
try:
    import tensorflow as tf
    HAS_TENSORFLOW = True
except ImportError:
    HAS_TENSORFLOW = False
    print("⚠️ TensorFlow未安装，LSTM功能将不可用")

try:
    from sklearn.preprocessing import MinMaxScaler
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    print("⚠️ Scikit-learn未安装，数据预处理功能受限")

class ModelManager:
    """LSTM模型管理器"""
    
    def __init__(self, model_version='current'):
        self.model_version = model_version
        self.model = None
        self.scaler = None
        self.features = get_features(model_version)
        self.model_path = get_model_path(model_version)
        self.scaler_path = get_scaler_path(model_version)
        self.is_loaded = False
        
        # 加载模型
        self.load_model()
    
    def load_model(self):
        """加载LSTM模型和数据预处理器"""
        try:
            # 加载LSTM模型
            if os.path.exists(self.model_path):
                if self.model_path.endswith('.h5') and HAS_TENSORFLOW:
                    # Keras模型
                    self.model = tf.keras.models.load_model(self.model_path)
                    print(f"✅ Keras模型加载成功: {self.model_version}")
                    print(f"   📁 模型路径: {self.model_path}")
                elif self.model_path.endswith('.pkl'):
                    # 演示模型
                    import pickle
                    with open(self.model_path, 'rb') as f:
                        model_data = pickle.load(f)

                    # 创建演示模型包装器
                    class DemoModelWrapper:
                        def __init__(self, model_data):
                            self.weights = model_data['weights']
                            self.bias = model_data['bias']
                            self.model_type = model_data.get('model_type', 'simple_linear')
                            self.features = model_data.get('features', [])

                        def predict(self, X, verbose=0):
                            try:
                                if len(X.shape) == 3:
                                    last_step = X[0, -1, :]
                                    prediction = np.dot(last_step, self.weights) + self.bias
                                    return prediction.reshape(1, 1)
                                return np.array([[0.02]])  # 默认预测2%收益
                            except Exception as e:
                                print(f"预测失败: {e}")
                                return np.array([[0.02]])

                    self.model = DemoModelWrapper(model_data)
                    print(f"✅ 演示模型加载成功: {self.model_version}")
                    print(f"   📁 模型路径: {self.model_path}")
                    print(f"   🏗️ 模型类型: {self.model.model_type}")
                else:
                    print(f"❌ 不支持的模型格式: {self.model_path}")
                    return False
            else:
                print(f"⚠️ LSTM模型文件未找到: {self.model_path}")
                return False
            
            # 加载数据标准化器
            if os.path.exists(self.scaler_path):
                with open(self.scaler_path, 'rb') as f:
                    scaler_data = pickle.load(f)

                # 检查是否是简单标准化器数据
                if isinstance(scaler_data, dict) and 'scaler_type' in scaler_data:
                    # 创建简单标准化器包装器
                    class SimpleScalerWrapper:
                        def __init__(self, scaler_data):
                            self.mean_ = scaler_data['mean_']
                            self.scale_ = scaler_data['scale_']
                            self.scaler_type = scaler_data['scaler_type']

                        def transform(self, X):
                            return (X - self.mean_) / self.scale_

                        def inverse_transform(self, X):
                            return X * self.scale_ + self.mean_

                    self.scaler = SimpleScalerWrapper(scaler_data)
                    print(f"✅ 简单标准化器加载成功")
                else:
                    # 标准的sklearn标准化器
                    self.scaler = scaler_data
                    print(f"✅ 数据标准化器加载成功")
            else:
                print(f"⚠️ 创建新的标准化器")
                if HAS_SKLEARN:
                    self.scaler = MinMaxScaler()
                else:
                    self.scaler = None
            
            self.is_loaded = True
            return True
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.is_loaded = False
            return False
    
    def prepare_data(self, stock_data):
        """准备模型输入数据"""
        if not self.is_loaded or stock_data is None:
            return None
        
        try:
            # 检查数据长度
            if len(stock_data) < LSTM_PARAMS['sequence_length']:
                print(f"⚠️ 数据长度不足，需要至少{LSTM_PARAMS['sequence_length']}天数据")
                return None
            
            # 检查特征列是否存在
            missing_features = [f for f in self.features if f not in stock_data.columns]
            if missing_features:
                print(f"⚠️ 缺少特征列: {missing_features}")
                return None
            
            # 提取特征数据
            feature_data = stock_data[self.features].tail(LSTM_PARAMS['sequence_length']).values
            
            # 检查数据完整性
            if np.isnan(feature_data).any():
                print("⚠️ 数据包含NaN值，进行填充处理")
                feature_data = pd.DataFrame(feature_data).fillna(method='ffill').fillna(method='bfill').values
            
            # 数据标准化
            if self.scaler is not None:
                # 如果scaler未训练，使用当前数据训练
                if not hasattr(self.scaler, 'scale_'):
                    self.scaler.fit(feature_data)
                
                feature_data = self.scaler.transform(feature_data)
            
            # 重塑为LSTM输入格式 (samples, timesteps, features)
            X = feature_data.reshape(1, LSTM_PARAMS['sequence_length'], len(self.features))
            
            return X
            
        except Exception as e:
            print(f"❌ 数据准备失败: {e}")
            return None
    
    def predict(self, stock_data):
        """使用LSTM模型进行预测"""
        if not self.is_loaded:
            return None
        
        try:
            # 准备输入数据
            X = self.prepare_data(stock_data)
            if X is None:
                return None
            
            # 模型预测
            prediction = self.model.predict(X, verbose=0)
            
            # 处理预测结果
            if prediction.shape[1] == 1:
                # 单一预测值（涨跌幅）
                return {
                    'prediction': float(prediction[0][0]),
                    'confidence': self.calculate_confidence(X),
                    'type': 'return_rate'
                }
            else:
                # 多个预测值（可能是多天预测）
                return {
                    'predictions': [float(p) for p in prediction[0]],
                    'confidence': self.calculate_confidence(X),
                    'type': 'multi_day'
                }
            
        except Exception as e:
            print(f"❌ 模型预测失败: {e}")
            return None
    
    def calculate_confidence(self, X):
        """计算预测置信度"""
        try:
            # 简单的置信度计算方法
            # 可以基于模型的不确定性或历史准确率
            
            # 方法1: 基于输入数据的稳定性
            data_stability = 1.0 - np.std(X) / (np.mean(np.abs(X)) + 1e-8)
            
            # 方法2: 基于模型复杂度（简化）
            model_confidence = 0.8  # 可以根据模型验证结果调整
            
            # 综合置信度
            confidence = (data_stability * 0.6 + model_confidence * 0.4)
            return max(0.0, min(1.0, confidence))
            
        except:
            return 0.5  # 默认置信度
    
    def evaluate_model(self, test_data, test_labels):
        """评估模型性能"""
        if not self.is_loaded:
            return None
        
        try:
            # 准备测试数据
            X_test = []
            for i in range(len(test_data) - LSTM_PARAMS['sequence_length']):
                X_test.append(test_data[i:i+LSTM_PARAMS['sequence_length']])
            X_test = np.array(X_test)
            
            # 模型预测
            predictions = self.model.predict(X_test, verbose=0)
            
            # 计算评估指标
            mse = mean_squared_error(test_labels, predictions)
            mae = mean_absolute_error(test_labels, predictions)
            rmse = np.sqrt(mse)
            
            # 计算准确率（预测方向正确的比例）
            pred_direction = np.sign(predictions.flatten())
            true_direction = np.sign(test_labels)
            direction_accuracy = np.mean(pred_direction == true_direction)
            
            return {
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'direction_accuracy': direction_accuracy,
                'sample_count': len(test_labels)
            }
            
        except Exception as e:
            print(f"❌ 模型评估失败: {e}")
            return None
    
    def save_scaler(self, scaler_path=None):
        """保存数据标准化器"""
        if self.scaler is None:
            return False
        
        try:
            save_path = scaler_path or self.scaler_path
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            print(f"✅ 标准化器保存成功: {save_path}")
            return True
            
        except Exception as e:
            print(f"❌ 标准化器保存失败: {e}")
            return False
    
    def get_model_info(self):
        """获取模型信息"""
        info = {
            'version': self.model_version,
            'model_path': self.model_path,
            'scaler_path': self.scaler_path,
            'features': self.features,
            'feature_count': len(self.features),
            'sequence_length': LSTM_PARAMS['sequence_length'],
            'is_loaded': self.is_loaded
        }
        
        if self.is_loaded and self.model:
            try:
                info.update({
                    'model_params': self.model.count_params(),
                    'input_shape': self.model.input_shape,
                    'output_shape': self.model.output_shape,
                    'layers': len(self.model.layers)
                })
            except:
                pass
        
        return info
    
    def predict_batch(self, stock_data_list):
        """批量预测"""
        if not self.is_loaded:
            return []
        
        results = []
        for i, stock_data in enumerate(stock_data_list):
            try:
                prediction = self.predict(stock_data)
                results.append(prediction)
            except Exception as e:
                print(f"❌ 批量预测第{i+1}个样本失败: {e}")
                results.append(None)
        
        return results

def test_model_manager():
    """测试模型管理器"""
    print("🧪 测试模型管理器...")
    
    # 测试不同版本的模型
    for version in ['current', 'v2.4.5', 'v1.3']:
        print(f"\n📊 测试模型版本: {version}")
        
        manager = ModelManager(version)
        info = manager.get_model_info()
        
        print(f"   ✅ 模型信息:")
        for key, value in info.items():
            print(f"      {key}: {value}")
        
        if manager.is_loaded:
            print(f"   ✅ 模型加载成功")
        else:
            print(f"   ❌ 模型加载失败")

if __name__ == "__main__":
    test_model_manager()
