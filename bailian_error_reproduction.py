#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云百炼平台API调用 - 修复版本
Alibaba Cloud Bailian Platform API Call - Fixed Version

✅ 使用官方推荐的DashScope API端点
✅ Using official recommended DashScope API endpoints

修复说明 Fix Notes:
- 原错误: 使用了错误的API端点导致403 Forbidden
- 解决方案: 改用官方DashScope端点，支持OpenAI兼容格式
- 参考文档: https://help.aliyun.com/zh/model-studio/text-generation

Original Error: Used wrong API endpoint causing 403 Forbidden
Solution: Switch to official DashScope endpoints with OpenAI-compatible format
Reference: https://help.aliyun.com/zh/model-studio/text-generation
"""

import requests
import json
from datetime import datetime

def main():
    """主函数 - 测试修复后的百炼平台API调用"""
    print("🚀 阿里云百炼平台API调用 - 修复版本")
    print("🚀 Alibaba Cloud Bailian Platform API Call - Fixed Version")
    print("=" * 80)
    print(f"运行时间 Runtime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 修复后的配置 - 使用官方DashScope端点
    config = {
        "api_key": "sk-17b7ed14b1bd4e80b311a5c696a7468c",
        "model": "qwen-plus"
    }

    # 使用官方推荐的DashScope API端点
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }

    # 使用OpenAI兼容格式
    data = {
        "model": config["model"],
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "请简单介绍一下股票市场的基本概念"}
        ]
    }
    
    print("📋 配置信息 Configuration Info:")
    print(f"   API密钥 API Key: {config['api_key']}")
    print(f"   模型 Model: {config['model']}")
    print(f"   请求URL Request URL: {url}")
    print(f"   格式 Format: OpenAI兼容格式")
    print()

    print("📝 请求详情 Request Details:")
    print(f"   方法 Method: POST")
    print(f"   Authorization: Bearer {config['api_key']}")
    print(f"   Content-Type: application/json")
    print(f"   请求体 Request Body:")
    print(f"   {json.dumps(data, ensure_ascii=False, indent=2)}")
    print()

    print("� 修复说明 Fix Notes:")
    print("   ❌ 原错误端点: https://bailian.aliyun.com/api/v1/apps/.../invoke")
    print("   ✅ 修复后端点: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions")
    print("   📚 参考文档: https://help.aliyun.com/zh/model-studio/text-generation")
    print()
    
    try:
        print("🚀 发送请求到百炼平台 Sending request to Bailian platform...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 实际响应状态码 Actual Response Status Code: {response.status_code}")
        print(f"📄 实际响应内容 Actual Response Content:")
        print(f"   {response.text}")
        print()
        
        print("📋 响应头信息 Response Headers:")
        for key, value in response.headers.items():
            print(f"   {key}: {value}")
        print()
        
        if response.status_code == 200:
            print("✅ API调用成功! API call successful!")
            print("🎉 修复生效，百炼平台API正常工作 Fix effective, Bailian platform API working normally")

            # 解析并显示AI回复
            try:
                result_json = response.json()
                ai_response = result_json["choices"][0]["message"]["content"]
                print(f"🤖 AI回复 AI Response:")
                print(f"   {ai_response[:200]}..." if len(ai_response) > 200 else f"   {ai_response}")
            except:
                print("📄 响应格式正确但解析失败")

            result = "API_FIXED"
        elif response.status_code == 403:
            print("❌ 仍然是403错误 Still 403 error")
            print("🔍 需要进一步检查API配置 Need further API configuration check")
            result = "STILL_ERROR"
        else:
            print(f"⚠️ 其他状态码 Other status code: {response.status_code}")
            result = "OTHER_STATUS"
            
    except Exception as e:
        print(f"❌ 请求异常 Request Exception: {str(e)}")
        result = "REQUEST_FAILED"
    
    # 生成curl命令 Generate curl command
    print("\n🔧 curl测试命令 curl Test Command:")
    print("=" * 60)
    print("工程师可以使用以下curl命令重现错误:")
    print("Engineers can use the following curl command to reproduce the error:")
    print()
    
    curl_cmd = f'''curl -X POST "{url}" \\
  -H "Authorization: Bearer {config['api_key']}" \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(data, ensure_ascii=False)}'
'''
    print(curl_cmd)
    
    # 结果总结 Result Summary
    print("\n📋 执行结果总结 Execution Result Summary:")
    print("=" * 60)

    if result == "API_FIXED":
        print("✅ 状态 Status: API修复成功 API Fixed Successfully")
        print("🎉 结果 Result: 百炼平台API正常工作 Bailian Platform API Working Normally")
        print("� 修复方法 Fix Method: 使用官方DashScope端点 Using Official DashScope Endpoints")
    elif result == "STILL_ERROR":
        print("❌ 状态 Status: 仍有错误 Still Error")
        print("🔍 建议 Suggestion: 检查API密钥权限 Check API Key Permissions")
    else:
        print(f"⚠️ 状态 Status: {result}")

    print()
    print(f"🔑 API密钥 API Key: {config['api_key']}")
    print(f"🎯 模型 Model: {config['model']}")
    print(f"🌐 端点 Endpoint: DashScope官方端点 Official DashScope Endpoint")
    print()
    print("📞 项目信息 Project Info:")
    print("   项目 Project: SmartStock-AI-Pro 智能股票分析系统")
    print("   用途 Purpose: 股票分析和投资建议生成 Stock analysis and investment advice generation")
    print("   状态 Status: API集成完成 API Integration Complete")
    print()
    print(f"⏰ 脚本完成时间 Script Completion Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 执行结果 Execution Result: {result}")

    return result == "API_FIXED"

if __name__ == "__main__":
    # 运行错误重现脚本 Run error reproduction script
    success = main()
    
    # 退出码 Exit code
    exit(0 if success else 1)
