# 🐺 狼眼看股市 - 综合短线选股系统

## 📋 系统简介

这是一个整合了您现有LSTM模型、TA-Lib技术指标和大模型分析的综合短线选股系统。系统目标是筛选出3日内涨幅达7%以上的A股股票。

## 🏗️ 系统架构

```
📁 项目结构
├── integrated_stock_selector.py  # 主选股程序
├── model_manager.py              # LSTM模型管理器
├── config.py                     # 配置文件
├── run_selector.py               # 启动脚本
├── A股模型/                      # 您的LSTM模型文件夹
│   ├── 股票预测模型-2.4.5-中国A股.ipynb
│   ├── 股票预测模型-1.3-中国A股.ipynb
│   ├── trained_model.h5          # 当前使用的模型
│   └── scaler.pkl                # 数据标准化器
└── INTEGRATED_SYSTEM_README.md   # 说明文档
```

## 🔧 系统组件

### 1. 数据层
- **Tushare Pro API**: 获取实时A股数据
- **历史数据**: 90天历史数据用于技术指标计算

### 2. 模型层
- **LSTM v2.4.5**: 13个技术指标，6.7MB模型
- **LSTM v1.3**: 价格和成交量特征
- **模型管理器**: 自动加载和管理不同版本模型

### 3. 技术分析层
- **TA-Lib指标**: RSI、MACD、布林带、KDJ等
- **评分系统**: 100分制综合技术评分
- **权重配置**: 可调整各指标权重

### 4. AI分析层
- **通义千问大模型**: 专业股票分析
- **智能评估**: 上涨概率、预期涨幅、买入时机
- **风险提示**: 主要风险点分析

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装必需依赖
pip install pandas numpy talib requests tushare scikit-learn

# 安装可选依赖（LSTM功能）
pip install tensorflow keras
```

### 2. 配置设置

编辑 `config.py` 文件：

```python
# API配置
TUSHARE_TOKEN = 'your_tushare_token'
LLM_API_KEY = 'your_llm_api_key'

# 模型路径（根据您的实际路径调整）
LSTM_MODEL_PATHS = {
    'current': 'A股模型/trained_model.h5'
}
```

### 3. 运行系统

```bash
# 方法1: 使用启动脚本（推荐）
python run_selector.py

# 方法2: 直接运行主程序
python integrated_stock_selector.py
```

## 📊 功能特性

### 🎯 选股策略
- **目标收益**: 3日内7%+涨幅
- **技术评分**: 100分制综合评分
- **多重筛选**: 技术指标 + LSTM预测 + AI分析

### 📈 技术指标
- **RSI**: 相对强弱指标 (权重20%)
- **MACD**: 指数平滑移动平均 (权重20%)
- **布林带**: 价格通道指标 (权重15%)
- **成交量**: 量价关系分析 (权重15%)
- **KDJ**: 随机指标 (权重10%)
- **趋势**: 均线多头排列 (权重10%)
- **波动率**: ATR波动率 (权重10%)

### 🤖 LSTM预测
- **模型版本**: 支持多版本模型切换
- **特征工程**: 13个技术指标特征
- **置信度**: 预测结果置信度评估
- **时间序列**: 60天历史数据预测

### 🧠 AI分析
- **专业分析**: 通义千问专业股票分析师
- **综合评估**: 技术面 + 基本面分析
- **操作建议**: 买入时机、止损位置
- **风险提示**: 主要风险点识别

## ⚙️ 配置说明

### 模型配置
```python
# 选择使用的模型版本
model_version = 'current'  # 'current', 'v2.4.5', 'v1.3'

# LSTM参数
LSTM_PARAMS = {
    'sequence_length': 60,      # 时间序列长度
    'prediction_days': 3,       # 预测天数
}
```

### 筛选条件
```python
FILTER_CONDITIONS = {
    'min_tech_score': 60,       # 最低技术评分
    'target_return': 7.0,       # 目标收益率(%)
    'holding_days': 3           # 持有天数
}
```

### 评分权重
```python
SCORING_WEIGHTS = {
    'RSI': 20,                  # RSI指标权重
    'MACD': 20,                 # MACD指标权重
    'BOLLINGER': 15,            # 布林带权重
    'VOLUME': 15,               # 成交量权重
    'KDJ': 10,                  # KDJ指标权重
    'TREND': 10,                # 趋势权重
    'VOLATILITY': 10            # 波动率权重
}
```

## 📋 输出示例

```
🐺======================================================================🐺
🐺        狼眼看股市 - 综合短线选股系统        🐺
🐺    LSTM模型 + TA-Lib指标 + 大模型分析    🐺
🐺======================================================================🐺
⏰ 分析时间: 2024-12-27 14:30:00
🎯 目标收益: 7.0% / 3天

🤖 初始化选股系统，模型版本: current
✅ 模型加载成功
   📊 特征数量: 13
   🔢 序列长度: 60

📊 开始分析10只候选股票...

🎯======================================================================🎯
🎯              综合选股结果              🎯
🎯======================================================================🎯

【 1】平安银行(000001.SZ)
     💰 当前价格:    12.45元
     📈 今日涨跌:  +2.15%
     📊 技术评分:  85/100
     🔍 技术信号: RSI适中(45.2), MACD金叉向上, 放量(1.8倍)
     🤖 LSTM预测: +5.23% (置信度: 78%)
     🧠 AI分析:
        概率：75%
        涨幅：6-8%
        买入：突破12.50元时买入
        止损：11.80元
        风险：大盘调整风险
----------------------------------------------------------------------
```

## 🔧 自定义配置

### 1. 修改股票池
```python
# 在config.py中修改
STOCK_POOLS = {
    'custom': [
        ("000001.SZ", "平安银行"),
        ("600519.SH", "贵州茅台"),
        # 添加您关注的股票
    ]
}
```

### 2. 调整技术指标参数
```python
TECHNICAL_PARAMS = {
    'MA_SHORT': 10,            # 短期均线
    'MA_LONG': 30,             # 长期均线
    'RSI_PERIOD': 14,          # RSI周期
    # 根据需要调整其他参数
}
```

### 3. 修改评分权重
```python
SCORING_WEIGHTS = {
    'RSI': 25,                 # 增加RSI权重
    'MACD': 25,                # 增加MACD权重
    'VOLUME': 20,              # 增加成交量权重
    # 调整其他权重，确保总和为100
}
```

## 🚨 注意事项

1. **API限制**: Tushare有调用频率限制，建议设置适当延时
2. **模型文件**: 确保LSTM模型文件路径正确
3. **数据质量**: 系统会自动处理缺失数据，但建议定期检查
4. **风险控制**: 本系统仅供参考，投资需谨慎
5. **实盘验证**: 建议先进行模拟交易验证

## 📞 技术支持

如有问题，请检查：
1. 依赖包是否完整安装
2. API密钥是否正确配置
3. 模型文件是否存在
4. 网络连接是否正常

## 📈 系统优势

- **多模型融合**: LSTM + 技术指标 + AI分析
- **专业评分**: 100分制综合技术评分
- **智能筛选**: 自动筛选高潜力股票
- **风险控制**: 内置风险评估机制
- **易于扩展**: 模块化设计，便于功能扩展

---

**免责声明**: 本系统仅供学习和研究使用，不构成投资建议。股市有风险，投资需谨慎。
