#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
狼眼看股市 - 综合短线选股系统
整合LSTM模型 + TA-Lib技术指标 + 大模型分析
目标：筛选3日内涨幅7%+的A股股票
"""

import pandas as pd
import numpy as np
import requests
import time
import json
import warnings
from datetime import datetime, timedelta

# 导入自定义模块
from config import *
from model_manager import ModelManager
from technical_indicators import add_all_indicators

warnings.filterwarnings('ignore')

class IntegratedStockSelector:
    def __init__(self, model_version='current'):
        self.model_manager = ModelManager(model_version)
        self.model_version = model_version
        print(f"🤖 初始化选股系统，模型版本: {model_version}")

        # 显示模型信息
        model_info = self.model_manager.get_model_info()
        if model_info['is_loaded']:
            print(f"✅ 模型加载成功")
            print(f"   📊 特征数量: {model_info['feature_count']}")
            print(f"   🔢 序列长度: {model_info['sequence_length']}")
        else:
            print("⚠️ 模型未加载，将仅使用技术指标分析")
    
    def call_ai_agent(self, prompt, model=None):
        """调用通义千问大模型"""
        if model is None:
            model = LLM_MODEL

        try:
            headers = {
                "Authorization": f"Bearer {LLM_API_KEY}",
                "Content-Type": "application/json"
            }

            data = {
                "model": model,
                "messages": [
                    {"role": "system", "content": "You are a professional short-term A-share stock analyst with expertise in technical analysis and market patterns. Please respond in Chinese."},
                    {"role": "user", "content": prompt}
                ]
            }

            response = requests.post(LLM_URL, headers=headers, json=data, timeout=DATA_PARAMS['timeout'])

            if response.status_code == 200:
                result = response.json()
                return result["choices"][0]["message"]["content"]
            else:
                return f"AI调用失败，状态码: {response.status_code}"
        except Exception as e:
            return f"AI调用异常: {str(e)}"
    
    def calculate_technical_indicators(self, df):
        """计算技术指标（使用自定义实现）"""
        try:
            # 使用自定义技术指标模块
            df = add_all_indicators(df)
            return df

        except Exception as e:
            print(f"技术指标计算失败: {e}")
            return df
    
    def lstm_predict(self, stock_data):
        """使用LSTM模型预测"""
        if not self.model_manager.is_loaded:
            return None

        try:
            # 使用模型管理器进行预测
            prediction_result = self.model_manager.predict(stock_data)

            if prediction_result is None:
                return None

            # 根据预测类型返回结果
            if prediction_result['type'] == 'return_rate':
                return {
                    'prediction': prediction_result['prediction'],
                    'confidence': prediction_result['confidence'],
                    'model_version': self.model_version
                }
            elif prediction_result['type'] == 'multi_day':
                # 取3日预测的平均值
                avg_prediction = np.mean(prediction_result['predictions'][:3])
                return {
                    'prediction': avg_prediction,
                    'confidence': prediction_result['confidence'],
                    'model_version': self.model_version,
                    'daily_predictions': prediction_result['predictions'][:3]
                }

            return prediction_result

        except Exception as e:
            print(f"LSTM预测失败: {e}")
            return None
    
    def calculate_short_term_score(self, stock_data, stock_info):
        """计算短线潜力评分"""
        score = 0
        reasons = []

        try:
            latest = stock_data.iloc[-1]

            # 1. RSI指标评分
            rsi = latest.get('RSI_14', 50)
            if 30 < rsi < 70:
                score += SCORING_WEIGHTS['RSI']
                reasons.append(f"RSI适中({rsi:.1f})")
            elif 20 < rsi < 30:
                score += int(SCORING_WEIGHTS['RSI'] * 0.75)
                reasons.append(f"RSI超卖反弹({rsi:.1f})")

            # 2. MACD指标评分
            macd = latest.get('MACD', 0)
            signal = latest.get('MACD_signal', 0)
            if macd > signal and macd > 0:
                score += SCORING_WEIGHTS['MACD']
                reasons.append("MACD金叉向上")
            elif macd > signal:
                score += int(SCORING_WEIGHTS['MACD'] * 0.75)
                reasons.append("MACD金叉")

            # 3. 布林带位置评分
            close = latest['close']
            bb_upper = latest.get('Bollinger_Upper', close * 1.1)
            bb_lower = latest.get('Bollinger_Lower', close * 0.9)
            bb_position = (close - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5

            if 0.2 < bb_position < 0.8:
                score += SCORING_WEIGHTS['BOLLINGER']
                reasons.append("布林带中轨")
            elif bb_position < 0.3:
                score += int(SCORING_WEIGHTS['BOLLINGER'] * 0.8)
                reasons.append("布林带下轨反弹")

            # 4. 成交量评分
            vol_ratio = latest.get('Volume_Ratio', 1)
            if vol_ratio > 1.5:
                score += SCORING_WEIGHTS['VOLUME']
                reasons.append(f"放量({vol_ratio:.1f}倍)")
            elif vol_ratio > 1.2:
                score += int(SCORING_WEIGHTS['VOLUME'] * 0.67)
                reasons.append("温和放量")

            # 5. KDJ指标评分
            k = latest.get('K', 50)
            d = latest.get('D', 50)
            if k > d and k < 80:
                score += SCORING_WEIGHTS['KDJ']
                reasons.append("KDJ金叉")

            # 6. 价格趋势评分
            ma10 = latest.get('MA_10', close)
            ma30 = latest.get('MA_30', close)
            if close > ma10 > ma30:
                score += SCORING_WEIGHTS['TREND']
                reasons.append("均线多头排列")

            # 7. 波动率评分
            atr = latest.get('ATR', 0)
            if atr > 0:
                volatility = atr / close
                if 0.02 < volatility < 0.06:  # 适度波动
                    score += SCORING_WEIGHTS['VOLATILITY']
                    reasons.append("波动率适中")

            return score, reasons

        except Exception as e:
            print(f"评分计算失败: {e}")
            return 0, ["评分计算失败"]
    
    def get_stock_data_with_indicators(self, stock_code, days=None):
        """获取股票数据并计算技术指标"""
        if days is None:
            days = DATA_PARAMS['history_days']

        try:
            import tushare as ts
            ts.set_token(TUSHARE_TOKEN)
            pro = ts.pro_api()

            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')

            # 获取日线数据
            df = pro.daily(ts_code=stock_code, start_date=start_date, end_date=end_date)

            if df.empty:
                return None

            # 计算技术指标
            df = self.calculate_technical_indicators(df)

            return df

        except Exception as e:
            print(f"获取股票数据失败 {stock_code}: {e}")
            return None
    
    def analyze_single_stock(self, stock_code, stock_name):
        """分析单只股票"""
        print(f"🔍 分析股票: {stock_name}({stock_code})")
        
        # 获取股票数据
        stock_data = self.get_stock_data_with_indicators(stock_code)
        if stock_data is None or len(stock_data) < 30:
            return None
        
        # 基础信息
        latest = stock_data.iloc[-1]
        stock_info = {
            'code': stock_code,
            'name': stock_name,
            'current_price': latest['close'],
            'change_pct': latest['pct_chg'],
            'volume': latest['vol'],
            'turnover': latest['amount']
        }
        
        # 计算技术评分
        tech_score, tech_reasons = self.calculate_short_term_score(stock_data, stock_info)
        
        # LSTM模型预测
        lstm_prediction = self.lstm_predict(stock_data)
        
        # AI分析
        ai_analysis = self.get_ai_analysis(stock_info, tech_score, tech_reasons, lstm_prediction)
        
        return {
            'stock_info': stock_info,
            'tech_score': tech_score,
            'tech_reasons': tech_reasons,
            'lstm_prediction': lstm_prediction,
            'ai_analysis': ai_analysis
        }
    
    def get_ai_analysis(self, stock_info, tech_score, tech_reasons, lstm_prediction):
        """获取AI分析"""
        # 构建LSTM预测信息
        lstm_info = ""
        if lstm_prediction:
            if isinstance(lstm_prediction, dict):
                pred_value = lstm_prediction.get('prediction', 0)
                confidence = lstm_prediction.get('confidence', 0.5)
                lstm_info = f"- LSTM预测: {pred_value:.2f}% (置信度: {confidence:.1%})"
            else:
                lstm_info = f"- LSTM预测: {lstm_prediction:.2f}%"
        else:
            lstm_info = "- LSTM预测: 暂无"

        prompt = f"""
        请分析A股股票 {stock_info['name']}({stock_info['code']})的短线交易机会：

        基本信息：
        - 当前价格: {stock_info['current_price']:.2f}元
        - 今日涨跌: {stock_info['change_pct']:.2f}%
        - 成交量: {stock_info['volume']:.0f}手
        - 成交额: {stock_info['turnover']:.0f}万元

        技术分析：
        - 技术评分: {tech_score}/100分
        - 技术信号: {', '.join(tech_reasons[:5])}
        {lstm_info}

        请判断该股票在未来{FILTER_CONDITIONS['holding_days']}个交易日内达到{FILTER_CONDITIONS['target_return']}%以上涨幅的可能性，并提供：

        1. 上涨概率: XX% (0-100之间)
        2. 预期涨幅: XX%
        3. 买入时机: 具体建议
        4. 止损位置: 建议价位
        5. 风险提示: 主要风险

        请用专业简洁的中文回答，格式：
        概率：XX%
        涨幅：XX%
        买入：XXX
        止损：XX元
        风险：XXX
        """

        return self.call_ai_agent(prompt)

def main():
    """主函数"""
    print("🐺" + "="*70 + "🐺")
    print("🐺        狼眼看股市 - 综合短线选股系统        🐺")
    print("🐺    LSTM模型 + TA-Lib指标 + 大模型分析    🐺")
    print("🐺" + "="*70 + "🐺")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标收益: {FILTER_CONDITIONS['target_return']}% / {FILTER_CONDITIONS['holding_days']}天")
    print()

    # 初始化选股系统（可以选择模型版本）
    model_version = 'current'  # 可选: 'current', 'v2.4.5', 'v1.3'
    selector = IntegratedStockSelector(model_version)

    # 使用配置的股票池
    sample_stocks = STOCK_POOLS['demo']

    print(f"📊 开始分析{len(sample_stocks)}只候选股票...")
    results = []

    for i, (stock_code, stock_name) in enumerate(sample_stocks, 1):
        try:
            print(f"   [{i:2d}/{len(sample_stocks)}] 分析 {stock_name}...")
            result = selector.analyze_single_stock(stock_code, stock_name)
            if result:
                results.append(result)
            time.sleep(1)  # 避免API限制
        except Exception as e:
            print(f"❌ 分析失败 {stock_name}: {e}")
            continue

    # 按综合评分排序并筛选
    if results:
        # 筛选符合条件的股票
        qualified_results = [r for r in results if r['tech_score'] >= FILTER_CONDITIONS['min_tech_score']]

        # 按技术评分排序
        qualified_results.sort(key=lambda x: x['tech_score'], reverse=True)

        print("\n🎯" + "="*70 + "🎯")
        print("🎯              综合选股结果              🎯")
        print("🎯" + "="*70 + "🎯")

        if qualified_results:
            for i, result in enumerate(qualified_results[:OUTPUT_CONFIG['max_results']], 1):
                stock = result['stock_info']
                print(f"\n【{i:2d}】{stock['name']}({stock['code']})")
                print(f"     💰 当前价格: {stock['current_price']:8.2f}元")
                print(f"     📈 今日涨跌: {stock['change_pct']:+6.2f}%")
                print(f"     📊 技术评分: {result['tech_score']:3d}/100")
                print(f"     🔍 技术信号: {', '.join(result['tech_reasons'][:3])}")

                # 显示LSTM预测
                if result['lstm_prediction']:
                    lstm_pred = result['lstm_prediction']
                    if isinstance(lstm_pred, dict):
                        pred_val = lstm_pred.get('prediction', 0)
                        confidence = lstm_pred.get('confidence', 0.5)
                        print(f"     🤖 LSTM预测: {pred_val:+5.2f}% (置信度: {confidence:.1%})")
                    else:
                        print(f"     🤖 LSTM预测: {lstm_pred:+5.2f}%")

                print(f"     🧠 AI分析:")
                ai_lines = result['ai_analysis'].split('\n')[:5]  # 显示前5行
                for line in ai_lines:
                    if line.strip():
                        print(f"        {line.strip()}")
                print("-" * 70)
        else:
            print(f"❌ 未找到技术评分≥{FILTER_CONDITIONS['min_tech_score']}分的股票")
            print("📊 所有分析结果:")
            for i, result in enumerate(results[:5], 1):
                stock = result['stock_info']
                print(f"   [{i}] {stock['name']}: {result['tech_score']}分")
    else:
        print("❌ 未获取到有效的分析结果")

    print(f"\n⏰ 分析完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📈 共分析 {len(sample_stocks)} 只股票，获得 {len(results)} 个有效结果")

if __name__ == "__main__":
    main()
