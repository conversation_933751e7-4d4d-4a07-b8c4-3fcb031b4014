#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算模块 - 不依赖TA-Lib的纯Python实现
"""

import pandas as pd
import numpy as np

def calculate_sma(data, window):
    """简单移动平均"""
    return data.rolling(window=window).mean()

def calculate_ema(data, window):
    """指数移动平均"""
    return data.ewm(span=window).mean()

def calculate_rsi(data, window=14):
    """相对强弱指标"""
    delta = data.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
    rs = gain / loss
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(data, fast=12, slow=26, signal=9):
    """MACD指标"""
    ema_fast = calculate_ema(data, fast)
    ema_slow = calculate_ema(data, slow)
    macd = ema_fast - ema_slow
    signal_line = calculate_ema(macd, signal)
    histogram = macd - signal_line
    return macd, signal_line, histogram

def calculate_bollinger_bands(data, window=20, std_dev=2):
    """布林带"""
    sma = calculate_sma(data, window)
    std = data.rolling(window=window).std()
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    return upper, sma, lower

def calculate_stoch(high, low, close, k_window=14, d_window=3):
    """KDJ指标"""
    lowest_low = low.rolling(window=k_window).min()
    highest_high = high.rolling(window=k_window).max()
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_window).mean()
    j_percent = 3 * k_percent - 2 * d_percent
    return k_percent, d_percent, j_percent

def calculate_atr(high, low, close, window=14):
    """平均真实波幅"""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(window=window).mean()
    return atr

def calculate_roc(data, window=1):
    """价格变化率"""
    return ((data - data.shift(window)) / data.shift(window)) * 100

def add_all_indicators(df):
    """为数据框添加所有技术指标"""
    try:
        # 确保数据按日期排序
        df = df.sort_values('trade_date').reset_index(drop=True)
        
        # 基础价格数据
        high = df['high']
        low = df['low']
        close = df['close']
        volume = df['vol']
        
        # 移动平均线
        df['MA_10'] = calculate_sma(close, 10)
        df['MA_30'] = calculate_sma(close, 30)
        
        # RSI
        df['RSI_14'] = calculate_rsi(close, 14)
        
        # MACD
        macd, signal, hist = calculate_macd(close)
        df['MACD'] = macd
        df['MACD_signal'] = signal
        df['MACD_hist'] = hist
        
        # 布林带
        upper, middle, lower = calculate_bollinger_bands(close)
        df['Bollinger_Upper'] = upper
        df['Bollinger_Middle'] = middle
        df['Bollinger_Lower'] = lower
        
        # KDJ指标
        k, d, j = calculate_stoch(high, low, close)
        df['K'] = k
        df['D'] = d
        df['J'] = j
        
        # 成交量指标
        df['Volume_MA'] = calculate_sma(volume, 5)
        df['Volume_Ratio'] = volume / (df['Volume_MA'] + 1e-8)  # 避免除零
        
        # 价格变化率
        df['Price_Change'] = calculate_roc(close, 1)
        
        # ATR (平均真实波幅)
        df['ATR'] = calculate_atr(high, low, close, 14)
        
        # 填充NaN值
        df = df.ffill().bfill()
        
        return df
        
    except Exception as e:
        print(f"技术指标计算失败: {e}")
        return df

def get_latest_signals(df):
    """获取最新的技术信号"""
    if df.empty:
        return {}
    
    latest = df.iloc[-1]
    signals = {}
    
    # RSI信号
    rsi = latest.get('RSI_14', 50)
    if rsi < 30:
        signals['RSI'] = 'oversold'
    elif rsi > 70:
        signals['RSI'] = 'overbought'
    else:
        signals['RSI'] = 'neutral'
    
    # MACD信号
    macd = latest.get('MACD', 0)
    signal = latest.get('MACD_signal', 0)
    if macd > signal:
        signals['MACD'] = 'bullish'
    else:
        signals['MACD'] = 'bearish'
    
    # 布林带信号
    close = latest['close']
    bb_upper = latest.get('Bollinger_Upper', close * 1.1)
    bb_lower = latest.get('Bollinger_Lower', close * 0.9)
    bb_position = (close - bb_lower) / (bb_upper - bb_lower) if bb_upper != bb_lower else 0.5
    
    if bb_position > 0.8:
        signals['Bollinger'] = 'overbought'
    elif bb_position < 0.2:
        signals['Bollinger'] = 'oversold'
    else:
        signals['Bollinger'] = 'neutral'
    
    # KDJ信号
    k = latest.get('K', 50)
    d = latest.get('D', 50)
    if k > d:
        signals['KDJ'] = 'bullish'
    else:
        signals['KDJ'] = 'bearish'
    
    # 趋势信号
    ma10 = latest.get('MA_10', close)
    ma30 = latest.get('MA_30', close)
    if close > ma10 > ma30:
        signals['Trend'] = 'bullish'
    elif close < ma10 < ma30:
        signals['Trend'] = 'bearish'
    else:
        signals['Trend'] = 'neutral'
    
    return signals

if __name__ == "__main__":
    # 测试代码
    print("技术指标计算模块测试")
    
    # 创建测试数据
    dates = pd.date_range('2024-01-01', periods=100, freq='D')
    np.random.seed(42)
    
    # 模拟股价数据
    base_price = 10
    returns = np.random.normal(0.001, 0.02, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    test_df = pd.DataFrame({
        'trade_date': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
        'close': prices,
        'vol': np.random.randint(1000, 10000, 100)
    })
    
    # 计算技术指标
    result_df = add_all_indicators(test_df)
    
    print("✅ 技术指标计算完成")
    print(f"数据行数: {len(result_df)}")
    print(f"指标列数: {len(result_df.columns)}")
    
    # 显示最新信号
    signals = get_latest_signals(result_df)
    print("\n最新技术信号:")
    for indicator, signal in signals.items():
        print(f"  {indicator}: {signal}")
